# Vue 3 + Element Plus 项目常见问题 FAQ

## 🔧 SVG Sprite Loader 相关问题

### Q1: 遇到 "Cannot find module 'webpack/lib/RuleSet'" 错误

**问题描述：**
```
ERROR in ./src/icons/svg/chart.svg
Module build failed (from ./node_modules/.pnpm/svg-sprite-loader@4.1.3_webpack@5.99.9/node_modules/svg-sprite-loader/lib/loader.js):
Error: Cannot find module 'webpack/lib/RuleSet'
```

**原因：**
`svg-sprite-loader` 4.1.3 版本与 webpack 5 不兼容

**解决方案：**
```bash
# 卸载旧版本并安装兼容版本
pnpm remove svg-sprite-loader
pnpm add -D svg-sprite-loader@6.0.11
```

---

## 🎨 SCSS 样式相关问题

### Q2: SCSS 变量未定义错误

**问题描述：**
```
Syntax Error: Undefined variable.
  ╷
6 │     margin-left: $sideBarWidth;
  │                  ^^^^^^^^^^^^^
  ╵
  src/styles/sidebar.scss 6:18
```

**原因：**
使用 `@use` 语法时，变量不会自动跨文件共享

**解决方案：**
在需要使用变量的 SCSS 文件顶部添加：
```scss
@use 'variables' as *;
```

---

## ⚛️ Vue 3 组件相关问题

### Q3: 函数式组件语法错误

**问题描述：**
```
TypeError: Cannot read properties of null (reading 'content')
```

**原因：**
_Vue 2 的函数式组件语法在 Vue 3 中已改变

**解决方案：**
```javascript
// Vue 2 写法 (错误)
export default {
  functional: true,
  render(h, context) {
    const { icon, title } = context.props
    // ...
  }
}

// Vue 3 写法 (正确)
import { h } from 'vue'
export default {
  props: { /* ... */ },
  render() {
    const { icon, title } = this
    return h('div', /* ... */)
  }
}
```

---

## 🛠️ Webpack 5 兼容性问题

### Q4: Node.js 核心模块 polyfill 缺失

**问题描述：**
```
Module not found: Error: Can't resolve 'path' in '...'
Module not found: Error: Can't resolve 'stream' in '...'
```

**原因：**
Webpack 5 不再自动提供 Node.js 核心模块的 polyfill

**解决方案：**
1. 安装 polyfill 包：
```bash
pnpm add -D path-browserify stream-browserify
```

2. 在 `vue.config.js` 中配置 fallback：
```javascript
module.exports = {
  configureWebpack: {
    resolve: {
      fallback: {
        "path": require.resolve("path-browserify"),
        "stream": require.resolve("stream-browserify")
      }
    }
  }
}
```

---

## 📦 依赖兼容性问题

### Q5: html-webpack-plugin 版本不兼容

**问题描述：**
```
ERROR TypeError: htmlWebpackPlugin.getHooks is not a function
```

**解决方案：**
```bash
# 升级到兼容版本
pnpm add -D html-webpack-plugin@5.6.0
```

同时移除不兼容的插件配置：
```javascript
// 在 vue.config.js 中移除 script-ext-html-webpack-plugin 配置
// 删除以下代码块：
config
  .plugin('ScriptExtHtmlWebpackPlugin')
  .after('html')
  .use('script-ext-html-webpack-plugin', [/* ... */])
```

---

## 🔄 从 Element UI 迁移到 Element Plus

### Q6: Element UI 模块找不到

**问题描述：**
```
Module not found: Error: Can't resolve 'element-ui' in '...'
```

1更新导入语句：
```javascript
// 旧的 Element UI 导入
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'

// 新的 Element Plus 导入
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
```

---

## 🔍 ESLint 配置问题

### Q7: ESLint 规则配置错误

**问题描述：**
```
Configuration for rule "vue/max-attributes-per-line" is invalid
```

**解决方案：**
更新 `.eslintrc.js` 中的 Vue 规则配置： https://github.com/vuejs/eslint-plugin-vue/issues/1628

```javascript
rules: {
  "vue/max-attributes-per-line": [
    "error",
    {
      "singleline": {
        "max": 3,
        "allowFirstLine": true
      },
      "multiline": {
        "max": 3,
        "allowFirstLine": true
      }
    }
  ]
}
```

## 1. Element UI 依赖错误

### ❌ 错误信息
```
Module not found: Error: Can't resolve 'element-ui' in '/Users/<USER>/src'
Module not found: Error: Can't resolve 'element-ui/lib/locale/lang/en'
```

### ✅ 解决方案
**修改 `src/main.js`：**
```javascript
// 旧版 (Vue 2 + Element UI)
import Vue from 'vue'
import Element from 'element-ui'
import './styles/element-variables.scss'
import enLang from 'element-ui/lib/locale/lang/en'

Vue.use(Element, {
  size: Cookies.get('size') || 'medium',
  locale: enLang
})

new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})

// 新版 (Vue 3 + Element Plus)
import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import enLang from 'element-plus/es/locale/lang/en'

const app = createApp(App)

app.use(ElementPlus, {
  size: Cookies.get('size') || 'default',
  locale: enLang
})

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(store).use(router)
app.mount('#app')
```

## 2. Element UI 组件引用错误

### ❌ 错误信息
```javascript
import { MessageBox, Message } from 'element-ui'
```

### ✅ 解决方案
**修改所有文件中的 Element UI 组件引用：**
```javascript
// 旧版
import { MessageBox, Message } from 'element-ui'

// 新版
import { ElMessageBox, ElMessage } from 'element-plus'

// 使用时也要更新
Message.error('错误') → ElMessage.error('错误')
MessageBox.confirm() → ElMessageBox.confirm()
```

## 3. Vue Router 4 API 变更错误

### ❌ 错误信息

```log
router.addRoutes is not a function
export 'default' (imported as 'Router') was not found in 'vue-router'
```

### ✅ 解决方案

**修改 `src/permission.js`：**
```javascript
// 旧版 (Vue Router 3)
router.addRoutes(accessRoutes)

// 新版 (Vue Router 4)
accessRoutes.forEach(route => {
  router.addRoute(route)
})
```

**修改路由创建方式：**
```javascript
// 旧版
import Router from 'vue-router'
Vue.use(Router)
export default new Router({...})

// 新版
import { createRouter, createWebHashHistory } from 'vue-router'
export default createRouter({
  history: createWebHashHistory(),
  routes: [...]
})
```

## 4. 缺失依赖包错误

### ❌ 错误信息
```
Module not found: Error: Can't resolve 'vue-splitpane'
```

### ✅ 解决方案
**替换为 Vue 3 兼容的包：**
```vue
// 旧版
import splitPane from 'vue-splitpane'

// 新版
import { Splitpanes, Pane } from 'splitpanes'
import 'splitpanes/dist/splitpanes.css'

// 模板也要更新
<split-pane split="vertical">
  <template slot="paneL">...</template>
  <template slot="paneR">...</template>
</split-pane>

// 改为
<splitpanes class="default-theme">
  <pane>...</pane>
  <pane>...</pane>
</splitpanes>
```

## 5. ESLint 配置错误

### ❌ 错误信息
```
Configuration for rule "vue/max-attributes-per-line" is invalid:
Value {"max":1,"allowFirstLine":false} should be number.
```

### ✅ 解决方案
**修改 `.eslintrc.js`：**
```javascript
// 旧版
"vue/max-attributes-per-line": [2, {
  "singleline": 10,
  "multiline": {
    "max": 1,
    "allowFirstLine": false
  }
}]

// 新版
"vue/max-attributes-per-line": [2, {
  "singleline": 10,
  "multiline": 1
}]
```

## 6. babel-eslint 解析错误

### ❌ 错误信息
```
Parsing error: require() of ES Module ... from babel-eslint not supported.
```

### ✅ 解决方案
**更新 ESLint 配置：**
```javascript
// 旧版
parserOptions: {
  parser: 'babel-eslint',
  sourceType: 'module'
}

// 新版 - 使用 @babel/eslint-parser
parserOptions: {
  parser: '@babel/eslint-parser',
  sourceType: 'module',
  requireConfigFile: false
}
```

## 7. 缺失视图文件错误

### ❌ 错误信息
```
Module not found: Error: Can't resolve '@/views/nested/menu1/index'
```

### ✅ 解决方案
**创建缺失的视图文件：**
```javascript
// src/views/nested/menu1/index.vue
<template>
  <div>
    <router-view />
  </div>
</template>

<script>
export default {
  name: 'Menu1'
}
</script>
```

## 8. Vue 3 过滤器移除错误

### ❌ 错误信息
```javascript
Vue.filter is not a function
```

### ✅ 解决方案
**将过滤器改为全局属性：**
```javascript
// 旧版
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})

// 新版
app.config.globalProperties.$filters = filters

// 模板中使用
{{ value | dateFormat }} → {{ $filters.dateFormat(value) }}
```

## 9. Sass 语法警告

### ⚠️ 警告信息
```
::v-deep usage as a combinator has been deprecated. Use :deep(<inner-selector>) instead
Using / for division outside of calc() is deprecated
```

### ✅ 解决方案
```scss
/* 旧版 */
::v-deep .el-input {
  color: red;
}
$width: 100px / 2;

/* 新版 */
:deep(.el-input) {
  color: red;
}
$width: math.div(100px, 2); // 或 calc(100px / 2)
```

## 10. Mock 文件缺失错误

### ❌ 错误信息
```
Module not found: Error: Can't resolve '../mock'
```

### ✅ 解决方案
**创建基础 mock 文件：**
```javascript
// mock/index.js
export function mockXHR() {
  console.log('Mock XHR initialized')
}
```

## 11. Vuex 4 API 变更

### ❌ 错误信息
```javascript
store.dispatch returns undefined
```

### ✅ 解决方案
**更新 Vuex 创建方式：**
```javascript
// 旧版
import Vuex from 'vuex'
Vue.use(Vuex)
export default new Vuex.Store({...})

// 新版
import { createStore } from 'vuex'
export default createStore({...})
```

## 12. 组合式 API 迁移

### ✅ 可选方案
**逐步迁移到组合式 API：**
```javascript
// 选项式 API (兼容)
export default {
  data() {
    return { count: 0 }
  },
  methods: {
    increment() {
      this.count++
    }
  }
}

// 组合式 API (推荐)
import { ref } from 'vue'
export default {
  setup() {
    const count = ref(0)
    const increment = () => count.value++
    return { count, increment }
  }
}
```

### Vue 2 到 Vue 3 迁移常见问题（FAQ）

#### **问题 1：升级 Vuex 后，应用启动时报错 "Cannot read properties of undefined (reading 'use')"，如何修复？**

**答：** 这个错误通常是因为 Vuex 4 (用于 Vue 3) 的初始化 API 与 Vuex 3 不同了。在 Vue 3 中，你不再需要通过 `Vue.use(Vuex)` 来“安装”Vuex。

*   **问题所在 (Vue 2 / Vuex 3 写法):**
    你的 `src/store/index.js` 文件可能还是这样写的：
    ```javascript
    import Vue from 'vue'
    import Vuex from 'vuex'

    Vue.use(Vuex)

    const store = new Vuex.Store({
      // ...
    })

    export default store
    ```

*   **解决方案 (Vue 3 / Vuex 4 写法):**
    你需要使用新的 `createStore` 函数来创建 store。
    1.  修改你的 import 语句。
    2.  用 `createStore()` 替换 `new Vuex.Store()`。
    3.  移除 `Vue.use(Vuex)` 这一行。

    更新后的 `src/store/index.js` 应该如下所示：
    ```javascript
    import { createStore } from 'vuex'
    import getters from './getters'
    // ... 其他模块导入

    const store = createStore({
      modules,
      getters
    })

    export default store
    ```

---

#### **问题 2：如何为 Vue 3 更新我的 Vue Router 路由配置？**

**答：** Vue Router 4 引入了一些重大变更，包括路由实例的创建方式和历史模式（history）的管理方式。

*   **问题所在 (Vue 2 / Vue Router 3 写法):**
    你的 `src/router/index.js` 可能还在使用 `new Router()` 和基于字符串的 `mode` 配置。同时，捕获所有路由（"catch-all"）的语法也不同了。
    ```javascript
    import Vue from 'vue'
    import Router from 'vue-router'

    Vue.use(Router)

    export default new Router({
      mode: 'history', // 旧的写法
      routes: [
        // ...
        { path: '*', redirect: '/404', hidden: true } // 旧的通配符路由
      ]
    })
    ```

*   **解决方案 (Vue 3 / Vue Router 4 写法):**
    使用新的 `createRouter` 和 `createWebHistory` (或 `createWebHashHistory`) 函数。
    1.  更新 `vue-router` 的 import 语句。
    2.  使用 `createRouter` 来创建路由实例。
    3.  提供一个 history 实现 (例如，`createWebHistory()` 对应 'history' 模式)。
    4.  更新通配符路由的语法。

    更新后的 `src/router/index.js` 大致如下：
    ```javascript
    import { createRouter, createWebHistory } from 'vue-router'
    // ...

    const router = createRouter({
      history: createWebHistory(process.env.BASE_URL),
      routes: constantRoutes
    })

    // 在你的路由数组中，需要更新通配符路由：
    // { path: '*', redirect: '/404' } 需要被修改为：
    { path: '/:pathMatch(.*)*', redirect: '/404', hidden: true }

    export default router
    ```

---

#### **问题 3：在 Vue 3 中如何注册全局组件（例如，一个全局的 SVG 图标组件）？**

**答：** 全局组件现在需要注册在由 `createApp` 创建的 `app` 实例上，而不是在全局的 `Vue` 对象上。

*   **问题所在 (Vue 2 写法):**
    你可能有一个文件，在其中直接使用 `Vue.component` 注册全局组件。
    ```javascript
    // src/icons/index.js
    import Vue from 'vue'
    import SvgIcon from '@/components/SvgIcon'
    Vue.component('svg-icon', SvgIcon)
    ```

*   **解决方案 (Vue 3 写法):**
    1.  修改组件文件 (`src/icons/index.js`)，让它导出组件而不是直接注册。
    2.  在 `src/main.js` 中导入该组件，并使用 `app.component()` 在 `app` 实例上注册。

    **第一步: 导出组件 (`src/icons/index.js`)**
    ```javascript
    import SvgIcon from '@/components/SvgIcon'
    // ... require.context 的代码 ...
    export { SvgIcon } // 导出组件
    ```
    **第二步: 在 `main.js` 中注册**
    ```javascript
    import { createApp } from 'vue'
    import App from './App'
    import { SvgIcon } from './icons' // 导入你的组件

    const app = createApp(App)

    app.component('svg-icon', SvgIcon) // 在 app 实例上注册全局组件

    app.mount('#app')
    ```

---

#### **问题 4：如何在 Vue 3 中配置全局错误处理器？**

** 全局错误处理器从 `Vue.config.errorHandler` 移到了 `app.config.errorHandler`。

*   **问题所在 (Vue 2 写法):**
    ```javascript
    // src/utils/error-log.js
    import Vue from 'vue'
    Vue.config.errorHandler = function(err, vm, info) {
      // ...
    }
    ```

*   **解决方案 (Vue 3 写法):**
    创建一个安装函数（setup function），该函数接收 `app` 实例作为参数，并在该函数中进行配置。
    1.  在你的工具文件中创建一个导出的安装函数。
    2.  在 `main.js` 中调用这个函数，并传入 `app` 实例。

    **第一步: 创建安装函数 (`src/utils/error-log.js`)**
    ```javascript
    export function setupErrorLog(app) {
      app.config.errorHandler = function(err, instance, info) {
        // 注意: `vm` 参数现在是 `instance`
        // ... 你的错误处理逻辑
      }
    }
    ```
    **第二步: 在 `main.js` 中使用它**
    ```javascript
    import { createApp } from 'vue'
    import App from './App'
    import { setupErrorLog } from './utils/error-log'

    const app = createApp(App)
    setupErrorLog(app) // 调用函数以设置错误处理器

    app.mount('#app')
    ```

希望这份 FAQ 对您有帮助！如果您还有其他问题，随时可以提出。


## Before error

```log
> vue-element-admin@4.4.0 dev
> vue-cli-service serve

 INFO  Starting development server...
 ERROR  ValidationError: Invalid options object. Dev Server has been initialized using an options object that does not match the API schema.
         - options has an unknown property 'before'. These properties are valid:
           object { allowedHosts?, bonjour?, client?, compress?, devMiddleware?, headers?, historyApiFallback?, host?, hot?, http2?, https?, ipc?, liveReload?, magicHtml?, onAfterSetupMiddleware?, onBeforeSetupMiddleware?, onListening?, open?, port?, proxy?, server?, setupExitSignals?, setupMiddlewares?, static?, watchFiles?, webSocketServer? }
ValidationError: Invalid options object. Dev Server has been initialized using an options object that does not match the API schema.
 - options has an unknown property 'before'. These properties are valid:
   object { allowedHosts?, bonjour?, client?, compress?, devMiddleware?, headers?, historyApiFallback?, host?, hot?, http2?, https?, ipc?, liveReload?, magicHtml?, onAfterSetupMiddleware?, onBeforeSetupMiddleware?, onListening?, open?, port?, proxy?, server?, setupExitSignals?, setupMiddlewares?, static?, watchFiles?, webSocketServer? }
```

去掉 before，改为 `setupMiddlewares`

````javascript
  devServer: {
    port: process.env.port || process.env.npm_config_port || 9527,
    open: true,
    // overlay: {
    //   warnings: false,
    //   errors: true
    // },
    // before: require('./mock/mock-server.js')
    setupMiddlewares(middlewares, devServer) {
      const mockServer = require('./mock/mock-server.js')
      mockServer(devServer.app)
      return middlewares
    }
  }
````


## ICON 问题

```javascript
import SvgIcon from '@/components/SvgIcon'// svg component

const req = require.context('./svg', false, /\.svg$/)
const requireAll = requireContext => requireContext.keys().map(requireContext)
requireAll(req)

// Export for main.js to register globally
export { SvgIcon }
```

## VueCompilerError: Error parsing JavaScript expression: Unexpected token, expected "," (3:22)

Gogocode 转换出错

修复前:

```vue
<el-button
  @click="
    UpdateVisible = true
    SelectId = scope.row.ruleId
  "
>修改</el-button>
```

修复后:

```vue
<el-button
  @click="() => { UpdateVisible = true; SelectId = scope.row.ruleId; }"
>修改</el-button>
```

## 🚨 启动阶段错误

### 异步组件导入错误

**错误现象：**
```bash
Cannot read properties of undefined (reading 'component')
TypeError: Cannot read properties of undefined (reading 'component')
    at eval (./src/icons/index.js:23:16)
```

**根本原因：** Vue 3 中异步组件的定义方式发生变化

**详细解决方案：**

1. **检查 src/icons/index.js 文件：**
```javascript
// ❌ Vue 2 写法可能导致问题
const req = require.context('./svg', false, /\.svg$/)
const requireAll = requireContext => requireContext.keys().map(requireContext)

// ✅ Vue 3 兼容写法
const modules = import.meta.glob('./svg/*.svg')
// 或者使用 require.context (如果支持)
```

2. **修复组件导入：**
```javascript
// ❌ 可能有问题的写法
export default {
  component: SomeComponent // undefined 导致错误
}

// ✅ 确保组件正确导入
import SomeComponent from './SomeComponent'
export default {
  component: SomeComponent
}
```

3. **异步图标组件处理：**
```javascript
// src/components/SvgIcon/index.vue
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'SvgIcon',
  props: {
    iconClass: {
      type: String,
      required: true
    },
    className: {
      type: String,
      default: ''
    }
  }
  // ... 其他配置
})
```

### loader 不是函数错误

**错误现象：**
```bash
TypeError: loader is not a function
    at load (vue/runtime-core/dist/runtime-core.esm-bundler.js:2544:62)
```

**根本原因：** Vue 3 中异步组件需要使用 `defineAsyncComponent`

**详细解决方案：**

1. **路由中的异步组件：**
```javascript
// ❌ Vue 2 写法
{
  path: '/redirect/:path(.*)', component: () => import('@/views/redirect/index')
}

// ✅ Vue 3 写法
import { defineAsyncComponent } from 'vue'

{
  path: '/redirect/:path(.*)', component: defineAsyncComponent(() => import('@/views/redirect/index'))
}
```

2. **组件中的异步子组件：**
```javascript
// ❌ Vue 2 写法
export default {
  components: {
    AsyncChild: () => import('./AsyncChild.vue')
  }
}

// ✅ Vue 3 写法
import { defineAsyncComponent } from 'vue'

export default {
  components: {
    AsyncChild: defineAsyncComponent(() => import('./AsyncChild.vue'))
  }
}
```

3. **带选项的异步组件：**
```javascript
// ✅ 高级用法
import { defineAsyncComponent } from 'vue'

const AsyncComponent = defineAsyncComponent({
  loader: () => import('./AsyncComponent.vue'),
  loadingComponent: LoadingComponent,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 3000
})
```

---

## 🗺️ 路由系统迁移

### 动态路由添加方式变更

**错误现象：**
```bash
router.addRoutes is not a function
```

**根本原因：** Vue Router 4 移除了 `addRoutes` 方法

**详细解决方案：**

1. **单个路由添加：**
```javascript
// ❌ Vue Router 3
router.addRoutes(accessRoutes)

// ✅ Vue Router 4 - 方式1：逐个添加
accessRoutes.forEach(route => {
  router.addRoute(route)
})

// ✅ Vue Router 4 - 方式2：添加到父路由
accessRoutes.forEach(route => {
  router.addRoute('parentRouteName', route)
})
```

2. **权限路由完整示例：**
```javascript
// src/permission.js
import { usePermissionStore } from '@/store/modules/permission'

// ❌ Vue 2 写法
const accessRoutes = await store.dispatch('permission/generateRoutes', roles)
router.addRoutes(accessRoutes)

// ✅ Vue 3 写法
const permissionStore = usePermissionStore()
const accessRoutes = await permissionStore.generateRoutes(roles)

// 清除旧路由（如果需要）
const removeRoutes = []
accessRoutes.forEach(route => {
  const removeRoute = router.addRoute(route)
  removeRoutes.push(removeRoute)
})

// 存储移除函数以便后续清理
permissionStore.setRemoveRoutes(removeRoutes)
```

3. **动态路由清理：**
```javascript
// 登出时清理动态路由
function resetRouter() {
  // 获取存储的移除函数
  const removeRoutes = permissionStore.removeRoutes
  removeRoutes.forEach(removeRoute => {
    removeRoute()
  })
  permissionStore.clearRemoveRoutes()
}
```

### 路由路径格式错误

**错误现象：**
```bash
Error: Route paths should start with a "/": "external-link" should be "/external-link"
```

**详细解决方案：**

1. **检查路由配置：**
```javascript
// ❌ 错误格式
const routes = [
  {
    path: 'dashboard', // 缺少前导斜杠
    component: Layout
  },
  {
    path: 'external-link', // 错误
    component: ExternalLink
  }
]

// ✅ 正确格式
const routes = [
  {
    path: '/dashboard',
    component: Layout
  },
  {
    path: '/external-link',
    component: ExternalLink
  }
]
```

2. **子路由路径规则：**
```javascript
// ✅ 父子路由正确配置
{
  path: '/system',
          component: Layout,
        children: [
  {
    path: '', // 子路由可以为空（匹配父路径）
    component: SystemIndex
  },
  {
    path: 'user', // 子路由不需要前导斜杠
    component: UserManagement
  },
  {
    path: '/independent', // 独立路径需要前导斜杠
    component: IndependentPage
  }
]
}
```

### 路由器创建方式变更

**Vue 2 vs Vue 3 对比：**

```javascript
// ❌ Vue Router 3 写法
import VueRouter from 'vue-router'
import Vue from 'vue'

Vue.use(VueRouter)

const createRouter = () => new VueRouter({
  mode: 'hash',
  routes: constantRoutes,
  scrollBehavior: () => ({ y: 0 })
})

// ✅ Vue Router 4 写法
import { createRouter, createWebHashHistory } from 'vue-router'

const createAppRouter = () => createRouter({
  history: createWebHashHistory(),
  routes: constantRoutes,
  scrollBehavior: () => ({ top: 0 })
})
```

---

## 🎯 组件系统变更

### DOM元素引用问题

**错误现象：**
```bash
Cannot read properties of undefined (reading 'addEventListener')
    at Proxy.mounted (ScrollPane.vue:22:24)
```

**根本原因：** Vue 3 中 DOM 渲染时机和引用方式有变化

**详细解决方案：**

1. **使用 nextTick 确保 DOM 已渲染：**
```javascript
// ❌ Vue 2 写法（可能在 Vue 3 中失效）
export default {
  mounted() {
    this.$refs.scrollPane.addEventListener('scroll', this.handleScroll)
  }
}

// ✅ Vue 3 兼容写法
import { nextTick } from 'vue'

export default {
  mounted() {
    nextTick(() => {
      if (this.$refs.scrollPane) {
        this.$refs.scrollPane.addEventListener('scroll', this.handleScroll)
      }
    })
  }
}
```

2. **Composition API 写法：**
```javascript
// ✅ 使用 Composition API
import { ref, onMounted, nextTick } from 'vue'

export default {
  setup() {
    const scrollPane = ref(null)

    const handleScroll = () => {
      // 处理滚动
    }

    onMounted(() => {
      nextTick(() => {
        if (scrollPane.value) {
          scrollPane.value.addEventListener('scroll', handleScroll)
        }
      })
    })

    return {
      scrollPane,
      handleScroll
    }
  }
}
```

3. **模板中的引用：**
```vue
<template>
  <!-- 确保 ref 名称匹配 -->
  <div ref="scrollPane" class="scroll-container">
    <!-- 内容 -->
  </div>
</template>
```

### 组件方法无法访问

**错误现象：**
```bash
_this2.$refs.scrollPane.moveToTarget is not a function
TypeError: _this2.$refs.scrollPane.moveToTarget is not a function
```

**根本原因：** Vue 3 中组件方法不再自动暴露给父组件

**详细解决方案：**

1. **子组件中暴露方法：**
```javascript
// 子组件 ScrollPane.vue
// ✅ Options API 写法
export default {
  methods: {
    moveToTarget(target) {
      // 方法实现
      console.log('Moving to target:', target)
    },
    scrollToElement(element) {
      // 另一个方法
    }
  },
  // 暴露方法给父组件
  expose: ['moveToTarget', 'scrollToElement']
}

// ✅ Composition API 写法
import { defineExpose } from 'vue'

export default {
  setup() {
    const moveToTarget = (target) => {
      // 方法实现
    }

    const scrollToElement = (element) => {
      // 方法实现
    }

    // 暴露方法
    defineExpose({
      moveToTarget,
      scrollToElement
    })

    return {
      // 内部使用的响应式数据
    }
  }
}
```

2. **<script setup> 语法：**
```vue
<!-- 子组件 ScrollPane.vue -->
<script setup>
  import { defineExpose } from 'vue'

  const moveToTarget = (target) => {
    // 方法实现
  }

  const scrollToElement = (element) => {
    // 方法实现
  }

  // 暴露方法
  defineExpose({
    moveToTarget,
    scrollToElement
  })
</script>
```

3. **父组件中调用：**
```javascript
// 父组件
export default {
  methods: {
    handleClick() {
      // 确保引用存在后再调用
      if (this.$refs.scrollPane && this.$refs.scrollPane.moveToTarget) {
        this.$refs.scrollPane.moveToTarget('some-target')
      }
    }
  }
}
```

---

## 🌐 全局API迁移

### 全局过滤器迁移

**错误现象：**
```bash
Cannot read properties of undefined (reading 'toThousandFilter')
TypeError: Cannot read properties of undefined (reading 'toThousandFilter')
```

**根本原因：** Vue 3 完全移除了过滤器功能

**详细解决方案：**

1. **创建过滤器函数文件：**
```javascript
// src/filters/index.js
export function toThousandFilter(num) {
  return (+num || 0).toString().replace(/^-?\d+/, (m) => m.replace(/(?=(?!\b)(\d{3})+$)/g, ','))
}

export function dateFilter(value, format = 'YYYY-MM-DD') {
  // 日期格式化实现
  return value ? dayjs(value).format(format) : ''
}

export function currencyFilter(value, currency = '¥') {
  return currency + toThousandFilter(value)
}

// 导出所有过滤器
export default {
  toThousandFilter,
  dateFilter,
  currencyFilter
}
```

2. **在 main.js 中注册为全局属性：**
```javascript
// main.js
import { createApp } from 'vue'
import App from './App.vue'
import filters from '@/filters'

const app = createApp(App)

// 方式1：逐个注册
Object.keys(filters).forEach(key => {
  app.config.globalProperties[key] = filters[key]
})

// 方式2：作为 $filters 对象注册（兼容旧代码）
app.config.globalProperties.$filters = filters

app.mount('#app')
```

3. **模板中使用：**
```vue
<template>
  <!-- ❌ Vue 2 过滤器语法 -->
  <span>{{ price | toThousandFilter }}</span>
  <span>{{ date | dateFilter('YYYY-MM-DD HH:mm') }}</span>

  <!-- ✅ Vue 3 全局属性语法 -->
  <span>{{ toThousandFilter(price) }}</span>
  <span>{{ dateFilter(date, 'YYYY-MM-DD HH:mm') }}</span>

  <!-- ✅ 兼容写法（如果注册了 $filters） -->
  <span>{{ $filters.toThousandFilter(price) }}</span>
  <span>{{ $filters.dateFilter(date, 'YYYY-MM-DD HH:mm') }}</span>
</template>
```

4. **组合式API中使用：**
```javascript
// 组件中
import { getCurrentInstance } from 'vue'
import { toThousandFilter } from '@/filters'

export default {
  setup() {
    // 方式1：直接导入使用
    const formatPrice = (price) => toThousandFilter(price)

    // 方式2：通过全局属性使用
    const { proxy } = getCurrentInstance()
    const formatPrice2 = (price) => proxy.toThousandFilter(price)

    return {
      formatPrice,
      formatPrice2
    }
  }
}
```

### 全局实例访问问题

**错误现象：**
```bash
Cannot read properties of undefined (reading 'config')
TypeError: Cannot read properties of undefined (reading 'config')
    at clipboardSuccess (clipboard.js:9:18)
```

**详细解决方案：**

1. **在 main.js 中暴露应用实例：**
```javascript
// main.js
import { createApp } from 'vue'
import App from './App.vue'

const app = createApp(App)

// 挂载到 window 对象供全局访问
window.$vueApp = app

// 或者更安全的方式，只暴露需要的 API
window.$message = app.config.globalProperties.$message
window.$notify = app.config.globalProperties.$notify

app.mount('#app')
```

2. **工具函数中使用：**
```javascript
// src/utils/clipboard.js
import Clipboard from 'clipboard'

function clipboardSuccess() {
  // ✅ 通过全局暴露的实例访问
  if (window.$vueApp) {
    window.$vueApp.config.globalProperties.$message.success('复制成功')
  }

  // ✅ 或使用直接暴露的方法
  if (window.$message) {
    window.$message.success('复制成功')
  }
}

function clipboardError() {
  if (window.$message) {
    window.$message.error('复制失败')
  }
}

export default function handleClipboard(text, event) {
  const clipboard = new Clipboard(event.target, {
    text: () => text
  })

  clipboard.on('success', () => {
    clipboardSuccess()
    clipboard.destroy()
  })

  clipboard.on('error', () => {
    clipboardError()
    clipboard.destroy()
  })

  clipboard.onClick(event)
}
```

3. **更优雅的解决方案 - 使用依赖注入：**
```javascript
// src/composables/useMessage.js
import { inject } from 'vue'

export function useMessage() {
  // 在组件中使用
  const message = inject('$message')
  return message
}

// main.js 中提供
app.provide('$message', app.config.globalProperties.$message)
```

---

## 🛠️ 构建配置调整

### process对象未定义

**错误现象：**
```bash
ReferenceError: process is not defined
    at Object.resolve (path-browserify/index.js:124:11)
```

**根本原因：** 浏览器环境没有 Node.js 的 `process` 对象

**详细解决方案：**

1. **Vue CLI 项目配置：**
```javascript
// vue.config.js
const webpack = require('webpack')

module.exports = {
  configureWebpack: {
    plugins: [
      new webpack.ProvidePlugin({
        process: 'process/browser',
        Buffer: ['buffer', 'Buffer']
      })
    ],
    resolve: {
      fallback: {
        process: require.resolve('process/browser'),
        buffer: require.resolve('buffer'),
        path: require.resolve('path-browserify'),
        os: require.resolve('os-browserify/browser'),
        crypto: require.resolve('crypto-browserify')
      }
    }
  }
}
```

2.**安装必要依赖：**
```bash
npm install --save-dev process buffer path-browserify os-browserify crypto-browserify
# 或
pnpm add -D process buffer path-browserify os-browserify crypto-browserify
```

### SVG 图标处理配置

**问题现象：** SVG 图标不显示或 SVGO 配置错误

**详细解决方案：**

1. **正确的 webpack 配置：**
```javascript
// vue.config.js
const path = require('path')

function resolve(dir) {
  return path.join(__dirname, dir)
}

module.exports = {
  chainWebpack: config => {
    // 排除 icons 目录中的 svg 文件，不让默认的 svg 规则处理
    config.module
            .rule('svg')
            .exclude.add(resolve('src/icons'))
            .end()

    // 专门处理 icons 目录中的 svg 文件
    config.module
            .rule('icons')
            .test(/\.svg$/)
            .include.add(resolve('src/icons'))
            .end()
            .use('svg-sprite-loader')
            .loader('svg-sprite-loader')
            .options({
              symbolId: 'icon-[name]'
            })
            .end()

    // 如果需要优化 svg
    config.module
            .rule('icons')
            .use('svgo-loader')
            .loader('svgo-loader')
            .options({
              plugins: [
                { name: 'removeViewBox', active: false },
                { name: 'removeEmptyAttrs', active: false }
              ]
            })
  }
}
```

2. **SVGO 版本兼容性处理：**
```bash
# 如果 SVGO 3.x 有问题，降级到 2.x
npm install svgo@^2.8.0 --save-dev
```

3. **图标组件更新：**
```vue
<!-- src/components/SvgIcon/index.vue -->
<template>
  <svg :class="svgClass" v-bind="$attrs" :style="{ color: color }">
    <use :xlink:href="iconName" :href="iconName" />
  </svg>
</template>

<script>
  import { defineComponent, computed } from 'vue'

  export default defineComponent({
    name: 'SvgIcon',
    inheritAttrs: false,
    props: {
      iconClass: {
        type: String,
        required: true
      },
      className: {
        type: String,
        default: ''
      },
      color: {
        type: String,
        default: ''
      }
    },
    setup(props) {
      const iconName = computed(() => `#icon-${props.iconClass}`)
      const svgClass = computed(() => {
        if (props.className) {
          return `svg-icon ${props.className}`
        }
        return 'svg-icon'
      })

      return {
        iconName,
        svgClass
      }
    }
  })
</script>
```

---

## 📦 第三方库适配

### ECharts兼容性问题

**错误现象：**
```bash
Cannot read properties of undefined (reading 'type')
    at echarts@4.2.1/lib/processor/dataSample.js:104:20
```

**根本原因：** ECharts 4.x 版本过旧，与现代构建工具兼容性差

**详细解决方案：**

1. **升级到 ECharts 5.x：**
```bash
# 卸载旧版本
npm uninstall echarts
# 安装新版本
npm install echarts@^5.5.0

# 或者使用 pnpm
pnpm add echarts@^5.5.0
```

2. **更新导入方式：**
```javascript
// ❌ ECharts 4.x 导入方式
import echarts from 'echarts'

// ✅ ECharts 5.x 导入方式
import * as echarts from 'echarts'

// ✅ 按需导入（推荐，减小打包体积）
import { init, use } from 'echarts/core'
import { BarChart, LineChart } from 'echarts/charts'
import { GridComponent } from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'

use([BarChart, LineChart, GridComponent, CanvasRenderer])
```

3. **组件中使用：**
```javascript
// ✅ Vue 3 + ECharts 5.x 完整示例
import { defineComponent, ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'

export default defineComponent({
  name: 'EchartsDemo',
  setup() {
    const chartRef = ref(null)
    let chartInstance = null

    const initChart = () => {
      if (chartRef.value) {
        chartInstance = echarts.init(chartRef.value)

        const option = {
          title: {
            text: 'ECharts 入门示例'
          },
          tooltip: {},
          xAxis: {
            data: ['衬衫', '羊毛衫', '雪纺衫', '裤子', '高跟鞋', '袜子']
          },
          yAxis: {},
          series: [{
            name: '销量',
            type: 'bar',
            data: [5, 20, 36, 10, 10, 20]
          }]
        }

        chartInstance.setOption(option)
      }
    }

    const resizeChart = () => {
      if (chartInstance) {
        chartInstance.resize()
      }
    }

    onMounted(() => {
      initChart()
      window.addEventListener('resize', resizeChart)
    })

    onUnmounted(() => {
      window.removeEventListener('resize', resizeChart)
      if (chartInstance) {
        chartInstance.dispose()
      }
    })

    return {
      chartRef
    }
  }
})
```
